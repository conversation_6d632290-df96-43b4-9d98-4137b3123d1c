/**
 * 该类负责启动HTTP服务器并处理传入的HTTP请求。它通过分析接口和实现类的元数据，
 * 将REST API路径映射到具体的服务实现类，并根据请求路径动态调用相应的方法。
 *
 * 功能包括：
 * 1. 启动HTTP服务器并监听指定端口。
 * 2. 初始化服务路径映射，将接口路径映射到实现类。
 * 3. 处理HTTP请求，根据请求路径动态调用相应的服务方法。
 * 4. 支持GET、POST、PUT等HTTP方法，并解析请求主体中的JSON参数。
 * 5. 返回方法调用结果作为JSON响应。
 */
// deno-lint-ignore-file no-explicit-any
import {
  apiImplMetaAnalyze,
  apiMetaAnalyze,
  ClassMeta
} from "./api-analyzer.ts";
import { createLogger } from "@common";
import { config } from "@/config/config.ts";
import { ApiMiddleware } from "./ApiMiddleware.ts";
import { Middleware } from "./Middleware.ts";

const logger = createLogger("AppServer");
export class AppServer {
  private apiPathToServicePahtMap = new Map<string, ClassMeta>();
  private middlewares: Middleware[] = [];

  constructor(appConfig: any) {
    Object.assign(config, appConfig);
  }

  public use(middleware: Middleware): void {
    this.middlewares.push(middleware);
  }

  public start() {
    this.initServicesPathMap();
    Deno.serve({ port: config.port }, this.handleRequest.bind(this));
  }

  private async initServicesPathMap() {
    const apiMeta = await apiMetaAnalyze(config.apiFolder);
    const implClassMeta = await apiImplMetaAnalyze(config.serviceFolder);

    implClassMeta.forEach((classMeta, interfaceName) => {
      const apiPath = apiMeta.get(interfaceName);
      if (apiPath) {
        this.apiPathToServicePahtMap.set(apiPath, classMeta);
        logger.debug(`REST服务URL映射关系-${apiPath} : ${classMeta.filePath}`);
      }
    });

    // 初始化默认中间件
    this.use(new ApiMiddleware(this.apiPathToServicePahtMap));
  }

  private async handleRequest(req: Request): Promise<Response> {
    let index = 0;

    const executeMiddleware = async (): Promise<Response> => {
      if (index < this.middlewares.length) {
        return this.middlewares[index++].handle(req, executeMiddleware);
      }
      return new Response("Not Found", { status: 404 });
    };

    return executeMiddleware();
  }
}

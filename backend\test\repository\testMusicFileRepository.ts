/**
 * MusicFileRepository 测试文件
 */
import { createLogger } from "@common";
import { musicFileRepository } from "@/repository/MusicFileRepository.ts";
import { MusicFile } from "@/api/files/MusicFile.ts";

const logger = createLogger("MusicFileRepository");


// 测试数据
const testMusicFile: MusicFile = {
  id: "test-music-file-001",
  path: "/test/music/song.mp3",
  fileName: "song.mp3",
  folderId: "test-folder-001",
  title: "测试歌曲",
  artist: "测试艺术家",
  album: "测试专辑",
  genre: "Pop",
  track: 1,
  year: 2024,
  duration: 180,
  sampleRate: 44100,
  bitRate: 320,
  channels: 2,
  format: "mp3",
  fileSize: 5242880, // 5MB
};

try {
  logger.info("=== MusicFileRepository 测试开始 ===");

  
  // 测试插入
  logger.info("1. 测试插入音乐文件...");
  musicFileRepository.insertMusicFile(testMusicFile);
  logger.info("✓ 插入成功");
  
  // 测试根据ID查询
  logger.info("2. 测试根据ID查询...");
  const foundById = musicFileRepository.findById(testMusicFile.id);
  logger.info("✓ 查询结果:", foundById?.title);
  
  // 测试根据路径查询
  logger.info("3. 测试根据路径查询...");
  const foundByPath = musicFileRepository.findByPath(testMusicFile.path);
  logger.info("✓ 查询结果:", foundByPath?.title);
  
  // 测试根据文件夹ID查询
  logger.info("4. 测试根据文件夹ID查询...");
  const foundByFolderId = musicFileRepository.findByFolderId(testMusicFile.folderId);
  logger.info("✓ 查询结果数量:", foundByFolderId.length);
  
  // 测试搜索
  logger.info("5. 测试搜索功能...");
  const searchResults = musicFileRepository.search("测试");
  logger.info("✓ 搜索结果数量:", searchResults.length);
  
  // 测试分页查询
  logger.info("6. 测试分页查询...");
  const paginationResult = musicFileRepository.findWithPagination(0, 10);
  logger.info("✓ 分页查询结果:", paginationResult.total, "条记录");
  
  // 测试更新
  logger.info("7. 测试更新音乐文件...");
  const updateSuccess = musicFileRepository.updateMusicFile(testMusicFile.id, {
    title: "更新后的标题",
    year: 2025
  });
  logger.info("✓ 更新结果:", updateSuccess);
  
  // 测试统计信息
  logger.info("8. 测试获取统计信息...");
  const stats = musicFileRepository.getStatistics();
  logger.info("✓ 统计信息:", stats);
  
  // 测试存在性检查
  logger.info("9. 测试存在性检查...");
  const exists = musicFileRepository.exists(testMusicFile.id);
  const pathExists = musicFileRepository.pathExists(testMusicFile.path);
  logger.info("✓ ID存在:", exists, "路径存在:", pathExists);
  
  // 测试删除
  logger.info("10. 测试删除音乐文件...");
  const deleteSuccess = musicFileRepository.deleteMusicFile(testMusicFile.id);
  logger.info("✓ 删除结果:", deleteSuccess);
  
  logger.info("=== 所有测试完成 ===");
  
} catch (error) {
  console.error("❌ 测试失败:", error);
} finally {
  // 关闭数据库连接
  musicFileRepository.close();
}

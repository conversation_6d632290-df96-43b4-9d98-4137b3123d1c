// AlbumServiceInterface.ts
import type { Album } from "./Album.ts";

export interface AlbumService {
  /**
   * 添加专辑
   * @param album 要添加的专辑对象
   * @returns 添加成功后的专辑对象
   */
  addAlbum(album: Album): Promise<Album>;

  /**
   * 获取所有专辑
   * @returns 包含所有专辑的数组
   */
  getAllAlbums(): Promise<Album[]>;

  /**
   * 根据专辑 ID 获取专辑信息
   * @param id 专辑的 ID
   * @returns 对应的专辑对象，如果不存在则返回 undefined
   */
  getAlbumById(id: number): Promise<Album | undefined>;

  /**
   * 根据专辑 ID 更新专辑信息
   * @param id 专辑的 ID
   * @param updatedAlbum 部分或全部更新后的专辑信息
   * @returns 更新成功后的专辑对象，如果专辑不存在则返回 undefined
   */
  updateAlbum(
    id: number,
    updatedAlbum: Partial<Album>,
  ): Promise<Album | undefined>;

  /**
   * 根据专辑 ID 删除专辑
   * @param id 专辑的 ID
   * @returns 删除操作是否成功
   */
  deleteAlbum(id: number): Promise<boolean>;
}

{"tasks": {"dev:backend": "deno run  --allow-all src/main.ts"}, "imports": {"@/": "./src/", "@common": "../common/src/mod.ts", "@std/dotenv": "jsr:@std/dotenv@^0.225.3", "@std/fs": "jsr:@std/fs@^1.0.14", "@std/path": "jsr:@std/path@^1.0.8", "@std/assert": "jsr:@std/assert@1", "@std/http": "jsr:@std/http@1", "@ts-morph": "https://deno.land/x/ts_morph@22.0.0/mod.ts", "@music-metadata": "npm:music-metadata@11", "@duckdb": "https://deno.land/x/duckdb/mod.ts"}}
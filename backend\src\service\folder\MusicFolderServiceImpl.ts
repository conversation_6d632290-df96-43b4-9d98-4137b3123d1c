import type { MusicFolder } from "@/api/folder/MusicFolder.ts";
import type { MusicFolderService } from "@/api/folder/MusicFolderService.ts";
import type { PageResult } from "@common";
import { musicRepository } from "@/repository/MusicRepository.ts";

export class MusicFolderServiceImpl implements MusicFolderService {
  // 分页查询文件夹
  async queryFolders(
    pageNum: number = 1,
    pageSize: number = 10,
  ): Promise<PageResult<MusicFolder>> {
    return musicRepository.queryFolders(pageNum, pageSize);
  }
}

/* global transition css */

/* fade */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.6s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* breadcrumb transition */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all .5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all .5s;
}

.breadcrumb-leave-active {
  position: absolute;
}
/* 从底部弹出渐变 */
.slide-enter-active,
.slide-leave-active {
  transition: all cubic-bezier(0.165, 0.84, 0.44, 1) 0.6s;
  z-index:30;
}

.slide-enter-from{
  opacity: 0;
  transform: translateY(80px);
}
.slide-enter-to{
  opacity: 1;
  transform: translateY(0px);
}
.slide-leave-to {
  opacity: 0;
  transform: translateY(80px);
}
.scale-enter-active,
.scale-leave-active {
  transition: all  ease-in-out .3s;
}
.scale-enter-to{
  opacity: 1;
  transform: scale(1.2);
}
.scale-leave-to{
  opacity: 0;
  transform: scale(1);
}
/*  */
.fade-in-scale-up-enter-active {
  transition: all cubic-bezier(.4, 0, .2, 1) .4s;
}
.fade-in-scale-up-leave-active{
  transition: all cubic-bezier(0, 0, .2, 1) .4s;
}
.fade-in-scale-up-enter-from,.fade-in-scale-up-leave-to{
  opacity: 0;
  transform: background-color .3s var(--n-bezier), box-shadow .3s var(--n-bezier) scale(0.9);
}
.fade-in-scale-up-leave-from,.fade-in-scale-up-enter-to{
  opacity: 1;
  transform: background-color .3s var(--n-bezier), box-shadow .3s var(--n-bezier) scale(1);
}
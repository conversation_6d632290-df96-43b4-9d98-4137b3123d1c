/**
 * 音乐文件分析器
 * <AUTHOR>
 * @date 2025-03-07
 */
import { walk } from "@std/fs";
import { basename, join } from "@std/path";
import { musicRepository } from "@/repository/MusicRepository.ts";
import { IdGenerator } from "@common";
import { MusicFolder } from "@/api/folder/MusicFolder.ts";
import { MusicFile } from "@/api/folder/MusicFile.ts";
import { parseFile } from "@music-metadata";
import { createLogger } from "@common";
import { FilesProcessingMiddleware } from "@/middleware/FilesProcessingMiddleware.ts";

const logger = createLogger("MusicAnalyzer");

export class MusicAnalyzer {
  private readonly supportedFormats = [".ape", ".flac", ".wav", ".mp3"];
  private readonly supportedImageFormats = [".jpg", ".jpeg", ".png"];

  async analyzeMusicFolder(folderPath: string): Promise<void> {
    for await (
      const entry of walk(folderPath, {
        includeDirs: true,
        includeFiles: true,
        maxDepth: 5,
      })
    ) {
      if (entry.isDirectory) {
        await this.processMusicFolder(entry.path);
      }
    }
  }

  private async processMusicFolder(folderPath: string): Promise<void> {
    const musicFiles = [];
    const imageFiles = [];

    for await (const entry of Deno.readDir(folderPath)) {
      const path = join(folderPath, entry.name);
      const ext = entry.name.toLowerCase();

      if (entry.isFile) {
        if (this.supportedFormats.some((format) => ext.endsWith(format))) {
          musicFiles.push(path);
        } else if (
          this.supportedImageFormats.some((format) => ext.endsWith(format))
        ) {
          imageFiles.push(path);
        }
      }
    }

    if (musicFiles.length > 0) {
      const folderId = IdGenerator.nextId();
      const now = new Date();
      //如果文件夹中没有封面图片，使用默认封面
      if (imageFiles.length <= 0) {
        imageFiles.push(join(Deno.cwd(), "/server/assets/cover.png"));
      }
      const imagePath = FilesProcessingMiddleware.pathPrefix +
        encodeURIComponent(imageFiles[0]); // 使用第一张图片作为封面
      const folder: MusicFolder = {
        id: folderId,
        folderName: basename(folderPath),
        folderPath: folderPath,
        coverImagePath: imagePath,
        createTime: now,
        updateTime: now,
      };
      logger.debug(`Processing folder:${folder.folderName}`);
      await musicRepository.insertFolder(folder);

      for (const musicFilePath of musicFiles) {
        await this.processMusicFile(musicFilePath, folderId);
      }
    }
  }

  private async processMusicFile(
    filePath: string,
    folderId: string,
  ): Promise<void> {
    try {
      const fileInfo = await Deno.stat(filePath);
      const metadata = await parseFile(filePath);

      const musicFile: MusicFile = {
        id: IdGenerator.nextId(),
        folderId: folderId,
        fileName: basename(filePath),
        filePath: filePath,
        fileFormat: filePath.split(".").pop()?.toLowerCase() || "",
        fileSize: fileInfo.size,
        duration: metadata.format.duration,
        bitRate: metadata.format.bitrate,
        sampleRate: metadata.format.sampleRate,
        album: metadata.common.album,
        artist: metadata.common.artist,
        title: metadata.common.title,
        genre: metadata.common.genre?.[0],
        year: metadata.common.year,
        createTime: new Date(),
        updateTime: new Date(),
      };
      logger.debug(`Processing file:`, JSON.stringify(musicFile));
      await musicRepository.insertMusicFile(musicFile);
    } catch (error) {
      console.error(`Error processing file ${filePath}:`, error);
    }
  }
}

const analyzer = new MusicAnalyzer();
await analyzer.analyzeMusicFolder("/mnt/e/Music");

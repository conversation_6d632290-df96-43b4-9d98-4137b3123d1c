<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="google-site-verification" content="jO2s61DEajT0Y9jksEjQ1s4dr8ntAKYIJoMdUA20u7g" />
  <link rel="icon" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link rel="stylesheet" crossorigin="anonymous"
    href="https://unpkg.byted-static.com/xgplayer/3.0.10/dist/index.min.css">
  <title>MUSEBOX</title>
  <style>
    #loading {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 80px;
      height: 80px;
    }

    .loading_container {
      width: 100%;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .loading_container>#loading>svg {
      stroke: #18a058;
    }

    .loadingMessage {
      font-size: 16px;
      color: #ccc;
      margin-top: 20px;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }

    .animate-spin {
      animation: spin 0.4s linear infinite;
    }

    .vue-recycle-scroller {
      position: relative
    }

    .vue-recycle-scroller.direction-vertical:not(.page-mode) {
      overflow-y: auto
    }

    .vue-recycle-scroller.direction-horizontal:not(.page-mode) {
      overflow-x: auto
    }

    .vue-recycle-scroller.direction-horizontal {
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex
    }

    .vue-recycle-scroller__slot {
      -webkit-box-flex: 1;
      -ms-flex: auto 0 0px;
      flex: auto 0 0
    }

    .vue-recycle-scroller__item-wrapper {
      -webkit-box-flex: 1;
      -ms-flex: 1;
      flex: 1;
      -webkit-box-sizing:
        border-box;
      box-sizing: border-box;
      overflow: hidden;
      position: relative
    }

    .vue-recycle-scroller.ready .vue-recycle-scroller__item-view {
      position: absolute;
      top: 0;
      left: 0;
      will-change: transform
    }

    .vue-recycle-scroller.direction-vertical .vue-recycle-scroller__item-wrapper {
      width: 100%
    }

    .vue-recycle-scroller.direction-horizontal .vue-recycle-scroller__item-wrapper {
      height: 100%
    }

    .vue-recycle-scroller.ready.direction-vertical .vue-recycle-scroller__item-view {
      width: 100%
    }

    .vue-recycle-scroller.ready.direction-horizontal .vue-recycle-scroller__item-view {
      height: 100%
    }

    .resize-observer[data-v-b329ee4c] {
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
      border: none;
      background-color: transparent;
      pointer-events: none;
      display: block;
      overflow: hidden;
      opacity: 0
    }

    .resize-observer[data-v-b329ee4c] object {
      display: block;
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      overflow: hidden;
      pointer-events: none;
      z-index: -1
    }
  </style>
</head>

<body>
  <div id="app">
    <div class="loading_container">
      <div id="loading">
        <svg file="#ccc" class="animate-spin" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
          <circle fill="none" stroke-width="16" stroke-linecap="round" cx="100" cy="100" r="92" stroke-dasharray="491"
            stroke-dashoffset="246">
          </circle>
        </svg>
        <span class="loadingMessage ">加载中...</span>
      </div>
    </div>
  </div>
  <script type="module" src="/src/main.ts"></script>
</body>

</html>
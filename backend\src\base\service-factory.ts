import { ClassMeta } from "./api-analyzer.ts";
const services: Map<string, any> = new Map();
export async function serviceFactory(key: string, classMeta: ClassMeta) {
  if (!services.has(key)) {
    let filePath = classMeta.filePath;
    if (!filePath.startsWith("file://")) {
      if (Deno.build.os === "windows") {
        filePath = "file:///" + filePath.replace(/\\/g, "/");
      } else {
        filePath = "file://" + filePath;
      }
    }
    const module = await import(filePath);
    const Class = module[classMeta.className];
    services.set(key, new Class());
  }
  return services.get(key);
}
/**
 * 音乐文件信息接口
 * <AUTHOR>
 * @date 2025-03-07
 */
export interface MusicFile {
  /** 文件唯一标识 */
  id: string;

  /** 所属文件夹ID */
  folderId: string;

  /** 文件名称 */
  fileName: string;

  /** 文件完整路径 */
  filePath: string;

  /** 文件格式（例如: mp3, flac, wav） */
  fileFormat: string;

  /** 文件大小（字节） */
  fileSize: number;

  /** 音频时长（秒） */
  duration?: number;

  /** 比特率（kbps） */
  bitRate?: number;

  /** 采样率（Hz） */
  sampleRate?: number;

  /** 专辑名称 */
  album?: string;

  /** 艺术家名称 */
  artist?: string;

  /** 音乐标题 */
  title?: string;

  /** 音乐流派 */
  genre?: string;

  /** 发行年份 */
  year?: number;

  /** 音轨编号 */
  trackNumber?: number;

  /** 创建时间 */
  createTime: Date;

  /** 更新时间 */
  updateTime: Date;
}

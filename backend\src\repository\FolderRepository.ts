
import { DatabaseSync } from "node:sqlite";
import { fromFileUrl } from "@std/path";
import { createLogger } from "@common";
import { Folder } from "@/api/folder/Folder.ts";

const logger = createLogger("FolderRepository");

export class FolderRepository {
  private db: DatabaseSync;

  constructor() {
    const dbUrl = import.meta.resolve("../../db/sqlite/musebox.db");
    const dbFilePath = fromFileUrl(dbUrl);
    this.db = new DatabaseSync(dbFilePath);
  }

  /**
   * 插入文件夹
   */
  insertFolder(folder: Folder): void {
    const stmt = this.db.prepare(`
      INSERT INTO folder (id, path, name, parentId, isLeaf, coverImagePath)
      VALUES (?, ?, ?, ?, ?, ?)
    `);
    
    try {
      stmt.run(
        folder.id,
        folder.path,
        folder.name,
        folder.parentId || null,
        folder.isLeaf,
        folder.coverImagePath || null
      );
      logger.debug(`插入文件夹成功: ${folder.name}`);
    } catch (error) {
      logger.error(`插入文件夹失败: ${folder.name}`, error);
      throw error;
    }
  }

  /**
   * 根据ID查询文件夹
   */
  findById(id: string): Folder | null {
    const stmt = this.db.prepare(`
      SELECT id, path, name, parentId, isLeaf, coverImagePath
      FROM folder WHERE id = ?
    `);
    
    try {
      const result = stmt.get(id);
      return result ? result as unknown as Folder : null;
    } catch (error) {
      logger.error(`查询文件夹失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 根据路径查询文件夹
   */
  findByPath(path: string): Folder | null {
    const stmt = this.db.prepare(`
      SELECT id, path, name, parentId, isLeaf, coverImagePath
      FROM folder WHERE path = ?
    `);
    
    try {
      const result = stmt.get(path);
      return result ? result as unknown as Folder : null;
    } catch (error) {
      logger.error(`根据路径查询文件夹失败: ${path}`, error);
      throw error;
    }
  }

  /**
   * 查询所有子文件夹
   */
  findChildren(parentId: string): Folder[] {
    const stmt = this.db.prepare(`
      SELECT id, path, name, parentId, isLeaf, coverImagePath
      FROM folder WHERE parentId = ?
      ORDER BY name
    `);
    
    try {
      return stmt.all(parentId) as unknown as Folder[];
    } catch (error) {
      logger.error(`查询子文件夹失败: ${parentId}`, error);
      throw error;
    }
  }

  /**
   * 查询所有叶子节点文件夹
   */
  findLeafFolders(): Folder[] {
    const stmt = this.db.prepare(`
      SELECT id, path, name, parentId, isLeaf, coverImagePath
      FROM folder WHERE isLeaf = 1
      ORDER BY name
    `);
    
    try {
      return stmt.all() as unknown as Folder[];
    } catch (error) {
      logger.error("查询叶子节点文件夹失败", error);
      throw error;
    }
  }

  /**
   * 查询根文件夹（没有父级的文件夹）
   */
  findRootFolders(): Folder[] {
    const stmt = this.db.prepare(`
      SELECT id, path, name, parentId, isLeaf, coverImagePath
      FROM folder WHERE parentId IS NULL
      ORDER BY name
    `);
    
    try {
      return stmt.all() as unknown as Folder[];
    } catch (error) {
      logger.error("查询根文件夹失败", error);
      throw error;
    }
  }

  /**
   * 分页查询文件夹
   */
  findWithPagination(offset: number, limit: number): { folders: Folder[], total: number } {
    const countStmt = this.db.prepare("SELECT COUNT(*) as count FROM folder");
    const dataStmt = this.db.prepare(`
      SELECT id, path, name, parentId, isLeaf, coverImagePath
      FROM folder
      ORDER BY name
      LIMIT ? OFFSET ?
    `);
    
    try {
      const countResult = countStmt.get() as { count: number };
      const folders = dataStmt.all(limit, offset) as unknown as Folder[];
      
      return {
        folders,
        total: countResult.count
      };
    } catch (error) {
      logger.error("分页查询文件夹失败", error);
      throw error;
    }
  }

  /**
   * 更新文件夹信息
   */
  updateFolder(id: string, updates: Partial<Omit<Folder, 'id'>>): boolean {
    const fields = [];
    const values = [];
    
    if (updates.path !== undefined) {
      fields.push("path = ?");
      values.push(updates.path);
    }
    if (updates.name !== undefined) {
      fields.push("name = ?");
      values.push(updates.name);
    }
    if (updates.parentId !== undefined) {
      fields.push("parentId = ?");
      values.push(updates.parentId);
    }
    if (updates.isLeaf !== undefined) {
      fields.push("isLeaf = ?");
      values.push(updates.isLeaf);
    }
    if (updates.coverImagePath !== undefined) {
      fields.push("coverImagePath = ?");
      values.push(updates.coverImagePath);
    }
    
    if (fields.length === 0) {
      return false;
    }
    
    values.push(id);
    
    const stmt = this.db.prepare(`
      UPDATE folder SET ${fields.join(", ")} WHERE id = ?
    `);
    
    try {
      const result = stmt.run(...values);
      const success = result.changes > 0;
      if (success) {
        logger.debug(`更新文件夹成功: ${id}`);
      }
      return success;
    } catch (error) {
      logger.error(`更新文件夹失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 删除文件夹
   */
  deleteFolder(id: string): boolean {
    const stmt = this.db.prepare("DELETE FROM folder WHERE id = ?");
    
    try {
      const result = stmt.run(id);
      const success = result.changes > 0;
      if (success) {
        logger.debug(`删除文件夹成功: ${id}`);
      }
      return success;
    } catch (error) {
      logger.error(`删除文件夹失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 检查文件夹是否存在
   */
  exists(id: string): boolean {
    const stmt = this.db.prepare("SELECT 1 FROM folder WHERE id = ?");
    
    try {
      const result = stmt.get(id);
      return result !== undefined;
    } catch (error) {
      logger.error(`检查文件夹存在性失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    this.db.close();
  }
}

// 导出单例实例
export const folderRepository = new FolderRepository();


{"name": "cloud-music-vue3", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview --port 5050", "test:unit": "vitest --environment jsdom", "typecheck": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@vueuse/core": "^11.2.0", "axios": "^1.7.7", "color": "^4.2.3", "colorthief": "^2.6.0", "dayjs": "^1.11.13", "eslint-plugin-tailwindcss": "^3.17.5", "express": "^4.21.1", "lodash": "^4.17.21", "nanoid": "^5.0.8", "normalize.css": "^8.0.1", "pinia": "^2.2.6", "qs": "^6.13.0", "unplugin-vue-components": "^0.27.4", "vue": "^3.5.13", "vue-router": "^4.4.5", "vue-virtual-scroller": "2.0.0-beta.8"}, "devDependencies": {"@rushstack/eslint-patch": "^1.10.4", "@types/color": "^4.2.0", "@types/jsdom": "^21.1.7", "@types/node": "^22.9.0", "@types/qs": "^6.9.17", "@vicons/antd": "^0.12.0", "@vicons/carbon": "^0.12.0", "@vicons/ionicons5": "^0.12.0", "@vicons/material": "^0.12.0", "@vitejs/plugin-vue": "^5.2.0", "@vitejs/plugin-vue-jsx": "^4.1.0", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.6.0", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-vue": "^9.31.0", "jsdom": "^25.0.1", "less": "^4.2.0", "naive-ui": "^2.40.1", "postcss": "^8.4.49", "prettier": "^3.3.3", "sass": "^1.81.0", "tailwindcss": "^3.4.15", "typescript": "~5.6.3", "vfonts": "^0.0.3", "vite": "^5.4.11", "vite-plugin-compression": "^0.5.1", "vitest": "^2.1.5", "vue-tsc": "^2.1.10"}, "type": "module"}
import { Middleware } from "@/base/Middleware.ts";

export class FilesProcessingMiddleware implements Middleware {
  static pathPrefix: string = "/files"; //访问路径前缀

  async handle(req: Request, next: () => Promise<Response>): Promise<Response> {
    const url = new URL(req.url);
    const path = decodeURIComponent(url.pathname);
    if (!path.startsWith(FilesProcessingMiddleware.pathPrefix)) {
      return next();
    }

    try {
      const filePath = path.replace(FilesProcessingMiddleware.pathPrefix, "");
      const file = await Deno.readFile(filePath);
      const contentType = this.getContentType(filePath);

      return new Response(file, {
        status: 200,
        headers: {
          "Content-Type": contentType,
          "Cache-Control": "public, max-age=31536000",
        },
      });
    } catch (error) {
      console.error("Error serving image:", error);
      return next();
    }
  }

  private getContentType(filePath: string): string {
    const ext = filePath.toLowerCase().split(".").pop();
    const contentTypes: Record<string, string> = {
      "jpg": "image/jpeg",
      "jpeg": "image/jpeg",
      "png": "image/png",
      "gif": "image/gif",
      "webp": "image/webp",
    };
    return contentTypes[ext ?? ""] || "application/octet-stream";
  }
}

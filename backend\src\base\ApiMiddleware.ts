// deno-lint-ignore-file no-explicit-any
import { Middleware } from "./Middleware.ts";
import { ClassMeta } from "./api-analyzer.ts";
import { serviceFactory } from "./service-factory.ts";
import { createLogger } from "@common";

const logger = createLogger("ApiMiddleware");

export class ApiMiddleware implements Middleware {
  constructor(private apiPathToServicePahtMap: Map<string, ClassMeta>) {}

  async handle(req: Request, next: () => Promise<Response>): Promise<Response> {
    const url = new URL(req.url);
    const path = url.pathname;
    const modulePath = path.split("/").slice(0, -1).join("/");
    
    const methodName = path.split("/").slice(-1).join("/");
    const classMeta = this.apiPathToServicePahtMap.get(modulePath);
    if (!classMeta) {
      return next();
    }

    try {
      const serviceInst = await serviceFactory(modulePath, classMeta);
      const method = serviceInst[methodName];

      if (typeof method !== "function") {
        return next();
      }

      let bodyParams: any = {};
      if (req.method === "POST" || req.method === "PUT") {
        try {
          bodyParams = await req.json();
          logger.debug(
            `Request url: ${path} Request body:${JSON.stringify(bodyParams)}`,
          );
        } catch {
          // Ignore JSON parsing errors
        }
      }

      let result = await method.bind(serviceInst)(...bodyParams);
      if (result instanceof Map) {
        result = Object.fromEntries(result);
      }

      return new Response(JSON.stringify(result), {
        status: 200,
        headers: { "Content-Type": "application/json" },
      });
    } catch (error) {
      console.error(`Error handling request for ${methodName}:`, error);
      return new Response("Internal Server Error", { status: 500 });
    }
  }
}

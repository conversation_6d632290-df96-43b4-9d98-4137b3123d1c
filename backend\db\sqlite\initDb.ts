import { fromFileUrl } from "@std/path";

// 方法1: 使用 fromFileUrl 转换 URL 为文件路径
const sqlFileUrl = import.meta.resolve("./ddl.sql");
const sqlFilePath = fromFileUrl(sqlFileUrl);
const sqlText = await Deno.readTextFile(sqlFilePath);

const dbUrl = import.meta.resolve("./musebox.db");
const dbFilePath = fromFileUrl(dbUrl);

import { DatabaseSync } from "node:sqlite";
const db = new DatabaseSync(dbFilePath);
db.exec(sqlText);
db.close();
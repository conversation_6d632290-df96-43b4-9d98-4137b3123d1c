-- ============================================
-- 表：folder
-- 说明：存储本地音乐库中的文件夹信息，支持多级目录结构，
--       叶子节点目录可附带封面图及统计信息。
-- ============================================
CREATE TABLE folder (
    id TEXT PRIMARY KEY,                    -- 主键，使用雪花算法生成的全局唯一 ID（字符串格式，防止数值精度问题）
    path TEXT NOT NULL UNIQUE,              -- 文件夹完整路径（唯一），用于定位和展示
    name TEXT NOT NULL,                     -- 文件夹名称（最后一级目录名）
    parent_id TEXT,                         -- 父文件夹 ID，引用同表的 id 字段，实现多级目录递归
    is_leaf INTEGER NOT NULL DEFAULT 0,     -- 是否为叶子节点目录：1=是，0=否，便于快速筛选最底层目录
    cover_image_path TEXT,                  -- 该文件夹的封面图片路径（选取该目录下的一张图片）
    FOREIGN KEY (parent_id) REFERENCES folder(id)  -- 外键约束：parent_id 必须是 folder 表中已存在的 id
);

-- 加速按父目录查询子目录
CREATE INDEX idx_folder_parent_id ON folder(parent_id);
-- 加速筛选所有叶子节点
CREATE INDEX idx_folder_is_leaf ON folder(is_leaf);



-- ============================================
-- 表：music_file
-- 说明：存储扫描到的音乐文件及其元数据，用于按文件夹或专辑聚合展示
-- ============================================
CREATE TABLE music_file (
    id TEXT PRIMARY KEY,                    -- 主键，雪花算法生成的全局唯一 ID
    path TEXT NOT NULL UNIQUE,              -- 音乐文件完整路径（唯一），用于播放或下载
    file_name TEXT NOT NULL,                -- 仅文件名（含扩展名），便于列表展示
    folder_id TEXT NOT NULL,                -- 所属文件夹 ID，关联 folder.id
    title TEXT,                             -- 音乐文件的元数据：标题
    artist TEXT,                            -- 艺术家
    album TEXT,                             -- 专辑名称
    genre TEXT,                             -- 音乐类别
    track INTEGER,                          -- 专辑中的曲目号
    year INTEGER,                           -- 发行年份
    duration INTEGER,                       -- 时长，单位：秒
    sample_rate INTEGER,                    -- 采样率，如 44100、48000
    bit_rate INTEGER,                       -- 码率，如 320（kbps）
    channels INTEGER,                       -- 声道数，如 2（立体声）
    format TEXT,                            -- 音频格式，如 MP3、FLAC、ALAC
    FOREIGN KEY (folder_id) REFERENCES folder(id)  -- 外键约束：folder_id 必须是 folder 表中已存在的 id
);

-- 加速按文件夹检索该目录下所有音乐文件
CREATE INDEX idx_music_file_folder_id ON music_file(folder_id);
-- 加速按专辑和艺术家聚合查询
CREATE INDEX idx_music_file_album_artist ON music_file(album, artist);
-- 加速按艺术家检索所有文件
CREATE INDEX idx_music_file_artist ON music_file(artist);



-- ============================================
-- 表：album
-- 说明：存储从 music_file 聚合而来的专辑信息，可直接展示专辑维度
-- ============================================
CREATE TABLE album (
    id TEXT PRIMARY KEY,                    -- 主键，雪花算法生成的全局唯一 ID
    name TEXT NOT NULL,                     -- 专辑名称
    artist TEXT,                            -- 专辑艺术家
    year INTEGER,                           -- 发行年份
    genre TEXT,                             -- 专辑整体类别
    folder_id TEXT,                         -- 专辑主要存储目录，可选，用于文件系统关联
    cover_image_path TEXT,                  -- 专辑封面图片路径（同样选取目录下的图片）
    UNIQUE(name, artist),                   -- 复合唯一：同一艺术家的同名专辑只保留一条
    FOREIGN KEY (folder_id) REFERENCES folder(id)  -- 外键约束：folder_id 必须是 folder 表中已存在的 id
);

-- 加速按专辑名称检索
CREATE INDEX idx_album_name ON album(name);
-- 加速按艺术家检索其所有专辑
CREATE INDEX idx_album_artist ON album(artist);

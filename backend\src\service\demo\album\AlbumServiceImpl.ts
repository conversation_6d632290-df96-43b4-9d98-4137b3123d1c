import { Album } from "@/api/demo/album/Album.ts";
import { AlbumService } from "@/api/demo/album/AlbumService.ts";

export class AlbumServiceImpl implements AlbumService {
  private albums: Album[] = [{
    id: 1,
    title: "title1",
    artist: "artist1",
    releaseDate: "releaseDate1", // 可以使用更具体的日期类型，如 Date，但为了简单这里用字符串
    genre: "genre",
  }];
  private nextId: number = 2;

  addAlbum(album: Album): Promise<Album> {
    album.id = this.nextId++;
    this.albums.push(album);
    return Promise.resolve(album);
  }

  getAllAlbums(): Promise<Album[]> {
    return Promise.resolve(this.albums);
  }

  getAlbumById(id: number): Promise<Album | undefined> {
    return Promise.resolve(this.albums.find((album) => album.id === id));
  }

  async updateAlbum(
    id: number,
    updatedAlbum: Partial<Album>,
  ): Promise<Album | undefined> {
    const album = await this.getAlbumById(id);
    if (!album) {
      return undefined;
    }
    Object.assign(album, updatedAlbum);
    return album;
  }

  deleteAlbum(id: number): Promise<boolean> {
    const index = this.albums.findIndex((album) => album.id === id);
    if (index === -1) {
      return Promise.resolve(false);
    }
    this.albums.splice(index, 1);
    return Promise.resolve(true);
  }
}

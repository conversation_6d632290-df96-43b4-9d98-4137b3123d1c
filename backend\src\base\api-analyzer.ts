import { Project } from "@ts-morph";
import { join } from "@std/path";
import { config } from "@/config/config.ts";
const currentWorkingDir = Deno.cwd();
export interface ClassMeta {
  className: string;
  filePath: string;
}

export async function apiMetaAnalyze(
  apiPath: string,
): Promise<Map<string, string>> {
  const interfaceMap = new Map<string, string>();
  const project = new Project();

  try {
    for await (const entry of Deno.readDir(apiPath)) {
      const relativePath = join(apiPath, entry.name); // 使用 join 代替字符串拼接
      if (entry.isDirectory) {
        const subDirMap = await apiMetaAnalyze(relativePath);
        subDirMap.forEach((value, key) => interfaceMap.set(key, value));
      } else if (entry.isFile && relativePath.endsWith(".ts")) {
        const sourceFile = project.addSourceFileAtPath(relativePath);
        sourceFile.getInterfaces().forEach((interfaceDeclaration) => {
          const apiName = interfaceDeclaration.getName();
          if (apiName.endsWith("Service")) {
            //存储接口名称和对应的URL路径
            interfaceMap.set(
              apiName,
              join("/", relativePath.replace(/\.ts$/, "").replace(join(config.apiFolder), join(config.apiPrefix))).replace(/\\/g, '/'),
            );
          }
        });
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${apiPath}:`, error);
    throw error;
  }

  return interfaceMap;
}

export async function apiImplMetaAnalyze(
  dirPath: string,
): Promise<Map<string, ClassMeta>> {
  const classMap = new Map<string, ClassMeta>();
  const project = new Project();

  for await (const entry of Deno.readDir(dirPath)) {
    const relativePath = join(dirPath, entry.name); // 使用 join 代替字符串拼接

    if (entry.isDirectory) {
      const subDirMap = await apiImplMetaAnalyze(relativePath);
      subDirMap.forEach((value, key) => classMap.set(key, value));
    } else if (entry.isFile && relativePath.endsWith(".ts")) {
      const sourceFile = project.addSourceFileAtPath(relativePath);
      sourceFile.getClasses().forEach((classDeclaration) => {
        const className = classDeclaration.getName();
        if (className?.endsWith("ServiceImpl")) {
          classDeclaration.getImplements().forEach((impl) => {
            const interfaceName = impl.getText();
            classMap.set(interfaceName, {
              className,
              filePath: join(currentWorkingDir, relativePath),
            });
          });
        }
      });
    }
  }

  return classMap;
}

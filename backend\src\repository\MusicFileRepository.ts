import { DatabaseSync } from "node:sqlite";
import { fromFileUrl } from "@std/path";
import { createLogger } from "@common";
import { MusicFile } from "@/api/files/MusicFile.ts";

const logger = createLogger("MusicFileRepository");

export class MusicFileRepository {
  private db: DatabaseSync;

  constructor() {
    const dbUrl = import.meta.resolve("../../db/sqlite/musebox.db");
    const dbFilePath = fromFileUrl(dbUrl);
    this.db = new DatabaseSync(dbFilePath);
  }

  /**
   * 插入音乐文件
   */
  insertMusicFile(musicFile: MusicFile): void {
    const stmt = this.db.prepare(`
      INSERT INTO music_file (
        id, path, fileName, folderId, title, artist, album, genre,
        track, year, duration, sampleRate, bitRate, channels, format, fileSize
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    try {
      stmt.run(
        musicFile.id,
        musicFile.path,
        musicFile.fileName,
        musicFile.folderId,
        musicFile.title || null,
        musicFile.artist || null,
        musicFile.album || null,
        musicFile.genre || null,
        musicFile.track || null,
        musicFile.year || null,
        musicFile.duration || null,
        musicFile.sampleRate || null,
        musicFile.bitRate || null,
        musicFile.channels || null,
        musicFile.format || null,
        musicFile.fileSize || null
      );
      logger.debug(`插入音乐文件成功: ${musicFile.fileName}`);
    } catch (error) {
      logger.error(`插入音乐文件失败: ${musicFile.fileName}`, error);
      throw error;
    }
  }

  /**
   * 根据ID查询音乐文件
   */
  findById(id: string): MusicFile | null {
    const stmt = this.db.prepare(`
      SELECT id, path, fileName, folderId, title, artist, album, genre,
             track, year, duration, sampleRate, bitRate, channels, format, fileSize
      FROM music_file WHERE id = ?
    `);

    try {
      const result = stmt.get(id);
      return result ? result as unknown as MusicFile : null;
    } catch (error) {
      logger.error(`查询音乐文件失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 根据路径查询音乐文件
   */
  findByPath(path: string): MusicFile | null {
    const stmt = this.db.prepare(`
      SELECT id, path, fileName, folderId, title, artist, album, genre,
             track, year, duration, sampleRate, bitRate, channels, format, fileSize
      FROM music_file WHERE path = ?
    `);

    try {
      const result = stmt.get(path);
      return result ? result as unknown as MusicFile : null;
    } catch (error) {
      logger.error(`根据路径查询音乐文件失败: ${path}`, error);
      throw error;
    }
  }

  /**
   * 根据文件夹ID查询所有音乐文件
   */
  findByFolderId(folderId: string): MusicFile[] {
    const stmt = this.db.prepare(`
      SELECT id, path, fileName, folderId, title, artist, album, genre,
             track, year, duration, sampleRate, bitRate, channels, format, fileSize
      FROM music_file WHERE folderId = ?
      ORDER BY fileName
    `);

    try {
      return stmt.all(folderId) as unknown as MusicFile[];
    } catch (error) {
      logger.error(`根据文件夹ID查询音乐文件失败: ${folderId}`, error);
      throw error;
    }
  }

  /**
   * 根据艺术家查询音乐文件
   */
  findByArtist(artist: string): MusicFile[] {
    const stmt = this.db.prepare(`
      SELECT id, path, fileName, folderId, title, artist, album, genre,
             track, year, duration, sampleRate, bitRate, channels, format, fileSize
      FROM music_file WHERE artist = ?
      ORDER BY album, track, fileName
    `);

    try {
      return stmt.all(artist) as unknown as MusicFile[];
    } catch (error) {
      logger.error(`根据艺术家查询音乐文件失败: ${artist}`, error);
      throw error;
    }
  }

  /**
   * 根据专辑查询音乐文件
   */
  findByAlbum(album: string, artist?: string): MusicFile[] {
    let sql = `
      SELECT id, path, fileName, folderId, title, artist, album, genre,
             track, year, duration, sampleRate, bitRate, channels, format, fileSize
      FROM music_file WHERE album = ?
    `;
    const params = [album];

    if (artist) {
      sql += ` AND artist = ?`;
      params.push(artist);
    }

    sql += ` ORDER BY track, fileName`;

    const stmt = this.db.prepare(sql);

    try {
      return stmt.all(...params) as unknown as MusicFile[];
    } catch (error) {
      logger.error(`根据专辑查询音乐文件失败: ${album}`, error);
      throw error;
    }
  }

  /**
   * 根据音乐类型查询音乐文件
   */
  findByGenre(genre: string): MusicFile[] {
    const stmt = this.db.prepare(`
      SELECT id, path, fileName, folderId, title, artist, album, genre,
             track, year, duration, sampleRate, bitRate, channels, format, fileSize
      FROM music_file WHERE genre = ?
      ORDER BY artist, album, track, fileName
    `);

    try {
      return stmt.all(genre) as unknown as MusicFile[];
    } catch (error) {
      logger.error(`根据音乐类型查询音乐文件失败: ${genre}`, error);
      throw error;
    }
  }

  /**
   * 搜索音乐文件（支持标题、艺术家、专辑模糊搜索）
   */
  search(keyword: string): MusicFile[] {
    const stmt = this.db.prepare(`
      SELECT id, path, fileName, folderId, title, artist, album, genre,
             track, year, duration, sampleRate, bitRate, channels, format, fileSize
      FROM music_file
      WHERE title LIKE ? OR artist LIKE ? OR album LIKE ? OR fileName LIKE ?
      ORDER BY artist, album, track, fileName
    `);

    const searchPattern = `%${keyword}%`;

    try {
      return stmt.all(searchPattern, searchPattern, searchPattern, searchPattern) as unknown as MusicFile[];
    } catch (error) {
      logger.error(`搜索音乐文件失败: ${keyword}`, error);
      throw error;
    }
  }

  /**
   * 分页查询音乐文件
   */
  findWithPagination(offset: number, limit: number, folderId?: string): { musicFiles: MusicFile[], total: number } {
    let countSql = "SELECT COUNT(*) as count FROM music_file";
    let dataSql = `
      SELECT id, path, fileName, folderId, title, artist, album, genre,
             track, year, duration, sampleRate, bitRate, channels, format, fileSize
      FROM music_file
    `;

    const params = [];

    if (folderId) {
      countSql += " WHERE folderId = ?";
      dataSql += " WHERE folderId = ?";
      params.push(folderId);
    }

    dataSql += " ORDER BY fileName LIMIT ? OFFSET ?";

    const countStmt = this.db.prepare(countSql);
    const dataStmt = this.db.prepare(dataSql);

    try {
      const countResult = folderId
        ? countStmt.get(folderId) as { count: number }
        : countStmt.get() as { count: number };

      const musicFiles = folderId
        ? dataStmt.all(folderId, limit, offset) as unknown as MusicFile[]
        : dataStmt.all(limit, offset) as unknown as MusicFile[];

      return {
        musicFiles,
        total: countResult.count
      };
    } catch (error) {
      logger.error("分页查询音乐文件失败", error);
      throw error;
    }
  }

  /**
   * 更新音乐文件信息
   */
  updateMusicFile(id: string, updates: Partial<Omit<MusicFile, 'id'>>): boolean {
    const fields = [];
    const values = [];
    
    if (updates.path !== undefined) {
      fields.push("path = ?");
      values.push(updates.path);
    }
    if (updates.fileName !== undefined) {
      fields.push("fileName = ?");
      values.push(updates.fileName);
    }
    if (updates.folderId !== undefined) {
      fields.push("folderId = ?");
      values.push(updates.folderId);
    }
    if (updates.title !== undefined) {
      fields.push("title = ?");
      values.push(updates.title);
    }
    if (updates.artist !== undefined) {
      fields.push("artist = ?");
      values.push(updates.artist);
    }
    if (updates.album !== undefined) {
      fields.push("album = ?");
      values.push(updates.album);
    }
    if (updates.genre !== undefined) {
      fields.push("genre = ?");
      values.push(updates.genre);
    }
    if (updates.track !== undefined) {
      fields.push("track = ?");
      values.push(updates.track);
    }
    if (updates.year !== undefined) {
      fields.push("year = ?");
      values.push(updates.year);
    }
    if (updates.duration !== undefined) {
      fields.push("duration = ?");
      values.push(updates.duration);
    }
    if (updates.sampleRate !== undefined) {
      fields.push("sampleRate = ?");
      values.push(updates.sampleRate);
    }
    if (updates.bitRate !== undefined) {
      fields.push("bitRate = ?");
      values.push(updates.bitRate);
    }
    if (updates.channels !== undefined) {
      fields.push("channels = ?");
      values.push(updates.channels);
    }
    if (updates.format !== undefined) {
      fields.push("format = ?");
      values.push(updates.format);
    }
    if (updates.fileSize !== undefined) {
      fields.push("fileSize = ?");
      values.push(updates.fileSize);
    }

    if (fields.length === 0) {
      return false;
    }
    
    values.push(id);
    
    const stmt = this.db.prepare(`
      UPDATE music_file SET ${fields.join(", ")} WHERE id = ?
    `);
    
    try {
      const result = stmt.run(...values);
      const success = result.changes > 0;
      if (success) {
        logger.debug(`更新音乐文件成功: ${id}`);
      }
      return success;
    } catch (error) {
      logger.error(`更新音乐文件失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 删除音乐文件
   */
  deleteMusicFile(id: string): boolean {
    const stmt = this.db.prepare("DELETE FROM music_file WHERE id = ?");
    
    try {
      const result = stmt.run(id);
      const success = result.changes > 0;
      if (success) {
        logger.debug(`删除音乐文件成功: ${id}`);
      }
      return success;
    } catch (error) {
      logger.error(`删除音乐文件失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 根据文件夹ID删除所有音乐文件
   */
  deleteByFolderId(folderId: string): number {
    const stmt = this.db.prepare("DELETE FROM music_file WHERE folderId = ?");

    try {
      const result = stmt.run(folderId);
      logger.debug(`删除文件夹下所有音乐文件成功: ${folderId}, 删除数量: ${result.changes}`);
      return Number(result.changes);
    } catch (error) {
      logger.error(`删除文件夹下音乐文件失败: ${folderId}`, error);
      throw error;
    }
  }

  /**
   * 检查音乐文件是否存在
   */
  exists(id: string): boolean {
    const stmt = this.db.prepare("SELECT 1 FROM music_file WHERE id = ?");
    
    try {
      const result = stmt.get(id);
      return result !== undefined;
    } catch (error) {
      logger.error(`检查音乐文件存在性失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 检查路径是否存在
   */
  pathExists(path: string): boolean {
    const stmt = this.db.prepare("SELECT 1 FROM music_file WHERE path = ?");
    
    try {
      const result = stmt.get(path);
      return result !== undefined;
    } catch (error) {
      logger.error(`检查音乐文件路径存在性失败: ${path}`, error);
      throw error;
    }
  }

  /**
   * 获取统计信息
   */
  getStatistics(): { totalFiles: number, totalDuration: number, totalSize: number } {
    const stmt = this.db.prepare(`
      SELECT 
        COUNT(*) as totalFiles,
        SUM(COALESCE(duration, 0)) as totalDuration,
        SUM(COALESCE(fileSize, 0)) as totalSize
      FROM music_file
    `);
    
    try {
      const result = stmt.get() as { totalFiles: number, totalDuration: number, totalSize: number };
      return result;
    } catch (error) {
      logger.error("获取音乐文件统计信息失败", error);
      throw error;
    }
  }

  /**
   * 关闭数据库连接
   */
  close(): void {
    this.db.close();
  }
}

// 导出单例实例
export const musicFileRepository = new MusicFileRepository();

MuseBox/ ├── backend/ # 后端代码目录 │ ├── src/ # 核心代码 │ │ ├── controllers/

# 控制器模块 │ │ │ ├── musicController.ts │ │ │ ├── userController.ts │ │ │ └──

... # 更多控制器 │ │ ├── models/ # 数据模型模块 │ │ │ ├── music.ts │ │ │ ├──
user.ts │ │ │ └── ... # 更多数据模型 │ │ ├── services/ # 服务模块 │ │ │ ├──
musicService.ts │ │ │ ├── userService.ts │ │ │ └── ... # 更多服务 │ │ ├──
middlewares/ # 中间件 │ │ │ ├── authMiddleware.ts │ │ │ ├── loggerMiddleware.ts
│ │ │ └── ... # 更多中间件 │ │ ├── utils/ # 工具函数 │ │ │ ├── fileUtils.ts │ │
│ ├── responseUtils.ts │ │ │ └── ... # 更多工具 │ │ ├── routes.ts # 路由配置 │ │
├── app.ts # 应用入口 │ │ └── deps.ts # 依赖统一导入 │ ├── config/ # 配置文件 │
│ ├── dbConfig.ts # 数据库配置 │ │ ├── appConfig.ts # 应用配置 │ │ └── ... #
更多配置 │ ├── tests/ # 测试代码 │ │ ├── musicController_test.ts │ │ ├──
userService_test.ts │ │ └── ... # 更多测试 │ ├── mod.ts # 后端入口文件 │ └──
README.md # 后端说明文档 ├── frontend/ # 前端代码目录 │ ├── public/ # 静态资源 │
│ ├── images/ │ │ ├── styles/ │ │ └── ... │ ├── src/ # 前端核心代码 │ │ ├──
components/ # 可复用组件 │ │ │ ├── Header.vue │ │ │ ├── Footer.vue │ │ │ └── ...

# 更多组件 │ │ ├── views/ # 页面视图 │ │ │ ├── Home.vue │ │ │ ├── Library.vue │

│ │ └── ... # 更多页面 │ │ ├── store/ # 状态管理 │ │ │ ├── index.ts │ │ │ └──
modules/ │ │ ├── router/ # 路由配置 │ │ │ ├── index.ts │ │ │ └── ...\
│ │ ├── utils/ # 工具函数 │ │ │ └── ... │ │ ├── App.vue # 应用入口 │ │ ├──
main.ts # 前端启动文件 │ │ └── ... # 更多前端代码 │ └── README.md # 前端说明文档
├── docs/ # 文档 │ ├── architecture.md # 系统架构设计文档 │ ├── api.md # API
接口文档 │ ├── requirements.md # 功能需求文档 │ └── ... # 更多文档 ├──
.gitignore # Git 忽略文件 ├── LICENSE # 项目许可 ├── README.md # 项目说明文档
└── deno.json # Deno 配置文件

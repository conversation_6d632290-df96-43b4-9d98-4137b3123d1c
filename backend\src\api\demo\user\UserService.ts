// musebox/shard/users/userServiceInterface.ts
import type { User } from "./User.ts";

export interface UserService {
  /**
   * 添加用户
   * @param user 要添加的用户对象
   * @returns 添加成功后的用户对象
   */
  addUser(user: User): Promise<User>;

  /**
   * 获取所有用户
   * @returns 包含所有用户的数组
   */
  getAllUsers(): Promise<User[]>;

  /**
   * 根据用户 ID 获取用户信息
   * @param id 用户的 ID
   * @returns 对应的用户对象，如果不存在则返回 undefined
   */
  getUserById(id: number): Promise<User | undefined>;

  /**
   * 根据用户 ID 更新用户信息
   * @param id 用户的 ID
   * @param updatedUser 部分或全部更新后的用户信息
   * @returns 更新成功后的用户对象，如果用户不存在则返回 undefined
   */
  updateUser(id: number, updatedUser: Partial<User>): Promise<User | undefined>;

  /**
   * 根据用户 ID 删除用户
   * @param id 用户的 ID
   * @returns 删除操作是否成功
   */
  deleteUser(id: number): Promise<boolean>;
}

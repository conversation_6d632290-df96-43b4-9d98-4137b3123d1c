/**
 * 音乐文件实体类
 */
export interface MusicFile {
  id: string;                    // 主键，雪花算法生成的全局唯一 ID
  path: string;                  // 音乐文件完整路径（唯一），用于播放或下载
  fileName: string;              // 仅文件名（含扩展名），便于列表展示
  folderId: string;              // 所属文件夹 ID，关联 folder.id
  title?: string;                // 音乐文件的元数据：标题
  artist?: string;               // 艺术家
  album?: string;                // 专辑名称
  genre?: string;                // 音乐类别
  track?: number;                // 专辑中的曲目号
  year?: number;                 // 发行年份
  duration?: number;             // 时长，单位：秒
  sampleRate?: number;           // 采样率，如 44100、48000
  bitRate?: number;              // 码率，如 320（kbps）
  channels?: number;             // 声道数，如 2（立体声）
  format?: string;               // 音频格式，如 MP3、FLAC、ALAC
  fileSize?: number;             // 文件大小（字节）
  createTime?: Date;             // 创建时间
  updateTime?: Date;             // 更新时间
}

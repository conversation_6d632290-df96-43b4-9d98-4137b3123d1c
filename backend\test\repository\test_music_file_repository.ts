/**
 * MusicFileRepository 测试文件
 */
import { musicFileRepository } from "../../src/repository/MusicFileRepository.ts";
import { MusicFile } from "../../src/api/files/MusicFile.ts";

// 创建测试实例
const repository = musicFileRepository;

// 测试数据
const testMusicFile: MusicFile = {
  id: "test-music-file-001",
  path: "/test/music/song.mp3",
  fileName: "song.mp3",
  folderId: "test-folder-001",
  title: "测试歌曲",
  artist: "测试艺术家",
  album: "测试专辑",
  genre: "Pop",
  track: 1,
  year: 2024,
  duration: 180,
  sampleRate: 44100,
  bitRate: 320,
  channels: 2,
  format: "mp3",
  fileSize: 5242880, // 5MB
};

try {
  console.log("=== MusicFileRepository 测试开始 ===");
  
  // 测试插入
  console.log("1. 测试插入音乐文件...");
  repository.insertMusicFile(testMusicFile);
  console.log("✓ 插入成功");
  
  // 测试根据ID查询
  console.log("2. 测试根据ID查询...");
  const foundById = repository.findById(testMusicFile.id);
  console.log("✓ 查询结果:", foundById?.title);
  
  // 测试根据路径查询
  console.log("3. 测试根据路径查询...");
  const foundByPath = repository.findByPath(testMusicFile.path);
  console.log("✓ 查询结果:", foundByPath?.title);
  
  // 测试根据文件夹ID查询
  console.log("4. 测试根据文件夹ID查询...");
  const foundByFolderId = repository.findByFolderId(testMusicFile.folderId);
  console.log("✓ 查询结果数量:", foundByFolderId.length);
  
  // 测试搜索
  console.log("5. 测试搜索功能...");
  const searchResults = repository.search("测试");
  console.log("✓ 搜索结果数量:", searchResults.length);
  
  // 测试分页查询
  console.log("6. 测试分页查询...");
  const paginationResult = repository.findWithPagination(0, 10);
  console.log("✓ 分页查询结果:", paginationResult.total, "条记录");
  
  // 测试更新
  console.log("7. 测试更新音乐文件...");
  const updateSuccess = repository.updateMusicFile(testMusicFile.id, {
    title: "更新后的标题",
    year: 2025
  });
  console.log("✓ 更新结果:", updateSuccess);
  
  // 测试统计信息
  console.log("8. 测试获取统计信息...");
  const stats = repository.getStatistics();
  console.log("✓ 统计信息:", stats);
  
  // 测试存在性检查
  console.log("9. 测试存在性检查...");
  const exists = repository.exists(testMusicFile.id);
  const pathExists = repository.pathExists(testMusicFile.path);
  console.log("✓ ID存在:", exists, "路径存在:", pathExists);
  
  // 测试删除
  console.log("10. 测试删除音乐文件...");
  const deleteSuccess = repository.deleteMusicFile(testMusicFile.id);
  console.log("✓ 删除结果:", deleteSuccess);
  
  console.log("=== 所有测试完成 ===");
  
} catch (error) {
  console.error("❌ 测试失败:", error);
} finally {
  // 关闭数据库连接
  repository.close();
}

// deno-lint-ignore-file
/**
 * 音乐数据库操作类
 * <AUTHOR>
 * @date 2025-03-07
 */
import { open } from "@duckdb";
import { MusicFolder } from "@/api/folder/MusicFolder.ts";
import { MusicFile } from "@/api/folder/MusicFile.ts";
import { PageResult } from "@common";
import { createLogger } from "@common";

const logger = createLogger("MusicRepository");
class MusicRepository {
  constructor() {
    this.initTables();
  }
  private async initTables() {
    const db = open("./music.db");
    const connection = db.connect();
    await connection.query(`
      CREATE TABLE IF NOT EXISTS music_folder (
        id VARCHAR PRIMARY KEY,
        folder_name VARCHAR NOT NULL,
        folder_path VARCHAR NOT NULL,
        cover_image_path VARCHAR,
        create_time VARCHAR NOT NULL,
        update_time VARCHAR NOT NULL
      )
    `);

    await connection.query(`
      CREATE TABLE IF NOT EXISTS music_files (
        id VARCHAR PRIMARY KEY,
        folder_id VARCHAR NOT NULL,
        file_name VARCHAR NOT NULL,
        file_path VARCHAR NOT NULL,
        file_format VARCHAR NOT NULL,
        file_size VARCHAR NOT NULL,
        duration VARCHAR,
        bit_rate VARCHAR,
        sample_rate VARCHAR,
        album VARCHAR,
        artist VARCHAR,
        title VARCHAR,
        genre VARCHAR,
        year VARCHAR,
        track_number VARCHAR,
        create_time VARCHAR NOT NULL,
        update_time VARCHAR NOT NULL
      )
    `);
    connection.close();
    db.close();
  }

  async insertFolder(folder: MusicFolder): Promise<void> {
    const db = open("./music.db");
    const connection = db.connect();

    try {
      const prepared = connection.prepare(`
        INSERT INTO music_folder (
          id, 
          folder_name, 
          folder_path, 
          cover_image_path, 
          create_time, 
          update_time
        )
        VALUES (?, ?, ?, ?, ?, ?)
      `);

      const params = [
        String(folder.id),
        String(folder.folderName),
        String(folder.folderPath),
        folder.coverImagePath ? String(folder.coverImagePath) : null,
        String(folder.createTime.toISOString()),
        String(folder.updateTime.toISOString()),
      ];

      prepared.query(...params);
    } finally {
      connection.close();
      db.close();
    }
  }

  async insertMusicFile(file: MusicFile): Promise<void> {
    const db = open("./music.db");
    const connection = db.connect();

    try {
      // 使用预处理语句（与insertFolder保持风格一致）
      const prepared = connection.prepare(`
        INSERT INTO music_files (
          id, folder_id, file_name, file_path, file_format, file_size,
          duration, bit_rate, sample_rate, album, artist, title,
          genre, year, track_number, create_time, update_time
        )
        VALUES (
          ?, ?, ?, ?, ?, ?, 
          ?, ?, ?, ?, ?, ?, 
          ?, ?, ?, ?, ?
        )
      `);

      prepared.query(
        String(file.id),
        String(file.folderId),
        String(file.fileName),
        String(file.filePath),
        String(file.fileFormat),
        String(file.fileSize),
        String(file.duration) ?? null,
        String(file.bitRate) ?? null,
        String(file.sampleRate) ?? null,
        String(file.album) ?? null,
        String(file.artist) ?? null,
        String(file.title) ?? null,
        String(file.genre) ?? null,
        String(file.year) ?? null,
        String(file.trackNumber) ?? null,
        String(file.createTime.toISOString()),
        String(file.updateTime.toISOString()),
      );
    } catch (error: any) {
      logger.error(`Error inserting music file: ${error.message}`);
      throw error;
    } finally {
      connection.close();
      db.close();
    }
  }

  /**
   * 分页查询音乐文件夹
   * @param pageNum 页码，默认第1页
   * @param pageSize 每页记录数，默认10条
   * @returns 分页结果
   */
  async queryFolders(
    pageNum: number = 1,
    pageSize: number = 10,
  ): Promise<PageResult<MusicFolder>> {
    const db = open("./music.db");
    const connection = db.connect();

    try {
      const countStmt = connection.prepare(
        `SELECT COUNT(*) as total FROM music_folder`,
      );
      const [countResult] = countStmt.query();
      const total = Number(countResult.total);

      const offset = (pageNum - 1) * pageSize;

      const stmt = connection.prepare(`
        SELECT 
          id, 
          folder_name as folderName, 
          folder_path as folderPath, 
          cover_image_path as coverImagePath,
          create_time as createTime,
          update_time as updateTime
        FROM music_folder
        ORDER BY update_time DESC
        LIMIT ? OFFSET ?
      `);

      // 关键修改：转换为 BigInt
      const folders = stmt.query(BigInt(pageSize), BigInt(offset));

      return {
        total,
        pageSize,
        pageNum,
        list: folders.map((folder: any) => ({
          id: String(folder.id),
          folderName: String(folder.folderName),
          folderPath: String(folder.folderPath),
          coverImagePath: folder.coverImagePath
            ? String(folder.coverImagePath)
            : null,
        })),
      };
    } finally {
      connection.close();
      db.close();
    }
  }
}
const musicRepository = new MusicRepository();
export { musicRepository };

// deno-lint-ignore-file no-explicit-any
import {
  <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  LoggerOptions as <PERSON><PERSON><PERSON>oggerOptions,
  pino,
} from "pino";
import "@std/dotenv/load";

/**
 * 封装日志工具的配置接口，后期若切换日志框架，仅需修改此处和内部实现
 */
export interface LoggerWrapperOptions extends PinoLoggerOptions {}

/**
 * 日志工具类
 *
 * 构造函数第一个参数 fileName 用来标识记录日志的模块或文件名称，
 * 第二个参数 options 为可选的 pino 构造参数选项。
 */
export class Logger {
  private logger: PinoLogger;

  constructor(private fileName: string, options?: LoggerWrapperOptions) {
    // 使用 pino 创建 logger 实例，设置 name 为 fileName
    this.logger = pino({
      name: fileName,
      level: Deno.env.get("LOG_LEVEL") || "info",
      transport: {
        target: "pino-pretty",
      },
      ...options,
    });
  }

  /**
   * 记录 info 日志
   */
  public info(...args: any[]): void {
    const message = args.join(" ");
    this.logger.info(message);
  }

  /**
   * 记录 warn 日志
   */
  public warn(...args: any[]): void {
    const message = args.join(" ");
    this.logger.warn(message);
  }

  /**
   * 记录 error 日志
   */
  public error(...args: any[]): void {
    const message = args.join(" ");
    this.logger.error(message);
  }

  /**
   * 记录 debug 日志
   */
  public debug(...args: any[]): void {
    const message = args.join(" ");
    this.logger.debug(message);
  }
}

/**
 * 日志工厂方法，便于调用时直接创建日志实例
 *
 * @param fileName 用于标识日志记录的模块或文件名称
 * @param options 可选的 pino 日志构造参数
 */
export function createLogger(
  fileName: string,
  options?: LoggerWrapperOptions,
): Logger {
  return new Logger(fileName, options);
}
